import { useState, useCallback, useRef, useEffect } from 'react';
import { MentionUser, MentionPopoverPosition, MentionHookResult, MENTION_TRIGGER, formatMentionForStorage } from '@/types/mention';

interface UseMentionsProps {
  users: MentionUser[];
  editorRef: React.RefObject<HTMLDivElement | null>;
  onMentionInsert?: (mentionText: string) => void;
}

export const useMentions = ({ users, editorRef, onMentionInsert }: UseMentionsProps): MentionHookResult => {
  const [showMentionPopover, setShowMentionPopover] = useState(false);
  const [mentionPosition, setMentionPosition] = useState<MentionPopoverPosition>({ top: 0, left: 0 });
  const [mentionQuery, setMentionQuery] = useState('');
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [mentionStartPos, setMentionStartPos] = useState<number | null>(null);
  
  const mentionRangeRef = useRef<Range | null>(null);

  // Filter users based on mention query
  const filteredUsers = users.filter(user => {
    const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
    const query = mentionQuery.toLowerCase();
    return fullName.includes(query) || user.firstName.toLowerCase().includes(query) || user.lastName.toLowerCase().includes(query);
  }).slice(0, 10); // Limit to 10 results

  // Reset selected index when filtered users change
  useEffect(() => {
    setSelectedMentionIndex(0);
  }, [filteredUsers.length]);

  // Get cursor position for popover placement
  const getCursorPosition = useCallback((): MentionPopoverPosition => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0 || !editorRef.current) {
      return { top: 0, left: 0 };
    }

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const editorRect = editorRef.current.getBoundingClientRect();

    return {
      top: rect.bottom - editorRect.top - 40, // Increased from 5px to 15px for more vertical offset
      left: rect.left - editorRect.left + 40  // Added 10px horizontal offset to move popover to the right
    };
  }, [editorRef]);

  // Detect mention trigger and show popover
  const detectMentionTrigger = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const textNode = range.startContainer;
    
    if (textNode.nodeType !== Node.TEXT_NODE) return;

    const textContent = textNode.textContent || '';
    const cursorPos = range.startOffset;
    
    // Find the last @ symbol before cursor
    let mentionStart = -1;
    for (let i = cursorPos - 1; i >= 0; i--) {
      const char = textContent[i];
      if (char === MENTION_TRIGGER) {
        mentionStart = i;
        break;
      }
      if (char === ' ' || char === '\n') {
        break;
      }
    }

    if (mentionStart !== -1) {
      const query = textContent.substring(mentionStart + 1, cursorPos);
      
      // Only show popover if query doesn't contain spaces (incomplete mention)
      if (!query.includes(' ') && !query.includes('\n')) {
        setMentionQuery(query);
        setMentionStartPos(mentionStart);
        setMentionPosition(getCursorPosition());
        setShowMentionPopover(true);
        
        // Store the range for later use
        const mentionRange = document.createRange();
        mentionRange.setStart(textNode, mentionStart);
        mentionRange.setEnd(textNode, cursorPos);
        mentionRangeRef.current = mentionRange;
        
        return;
      }
    }

    // Hide popover if no valid mention trigger found
    if (showMentionPopover) {
      closeMentionPopover();
    }
  }, [editorRef, showMentionPopover, getCursorPosition]);

  // Handle mention selection
  const handleMentionSelect = useCallback((user: MentionUser) => {
    if (!editorRef.current || !mentionRangeRef.current) return;

    const displayName = `${user.firstName} ${user.lastName}`;
    const mentionText = formatMentionForStorage(displayName, user.id);
    
    // Create mention HTML element
    const mentionSpan = document.createElement('span');
    mentionSpan.className = 'mention';
    mentionSpan.setAttribute('data-user-id', user.id.toString());
    mentionSpan.setAttribute('data-mention', 'true');
    mentionSpan.textContent = `@${displayName}`;
    mentionSpan.style.cssText = `
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 2px 4px;
      border-radius: 4px;
      font-weight: 500;
      text-decoration: none;
    `;

    // Replace the mention trigger and query with the mention element
    const range = mentionRangeRef.current;
    range.deleteContents();
    range.insertNode(mentionSpan);
    
    // Add a space after the mention
    const spaceNode = document.createTextNode(' ');
    range.setStartAfter(mentionSpan);
    range.insertNode(spaceNode);
    
    // Move cursor after the space
    const selection = window.getSelection();
    if (selection) {
      range.setStartAfter(spaceNode);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }

    closeMentionPopover();
    
    // Trigger change event
    if (onMentionInsert) {
      onMentionInsert(mentionText);
    }
  }, [editorRef, onMentionInsert]);

  // Handle keyboard navigation in mention popover
  const handleMentionKeyDown = useCallback((e: React.KeyboardEvent): boolean => {
    if (!showMentionPopover || filteredUsers.length === 0) return false;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedMentionIndex(prev => 
          prev < filteredUsers.length - 1 ? prev + 1 : 0
        );
        return true;
        
      case 'ArrowUp':
        e.preventDefault();
        setSelectedMentionIndex(prev => 
          prev > 0 ? prev - 1 : filteredUsers.length - 1
        );
        return true;
        
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        if (filteredUsers[selectedMentionIndex]) {
          handleMentionSelect(filteredUsers[selectedMentionIndex]);
        }
        return true;
        
      case 'Escape':
        e.preventDefault();
        closeMentionPopover();
        return true;
        
      default:
        return false;
    }
  }, [showMentionPopover, filteredUsers, selectedMentionIndex, handleMentionSelect]);

  // Close mention popover
  const closeMentionPopover = useCallback(() => {
    setShowMentionPopover(false);
    setMentionQuery('');
    setMentionStartPos(null);
    setSelectedMentionIndex(0);
    mentionRangeRef.current = null;
  }, []);

  // Expose detection function for use in editor
  const checkForMentionTrigger = useCallback(() => {
    detectMentionTrigger();
  }, [detectMentionTrigger]);

  return {
    showMentionPopover,
    mentionPosition,
    mentionQuery,
    filteredUsers,
    selectedMentionIndex,
    handleMentionKeyDown,
    handleMentionSelect,
    closeMentionPopover,
    // Additional method for manual trigger detection
    checkForMentionTrigger
  } as MentionHookResult & { checkForMentionTrigger: () => void };
};
