'use client';

import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import {
  Send,
  MessageCircle,
  Users,
  Building2,
  CheckSquare,
  CheckCheck,
  Image as ImageIcon,
  X,
  Loader2,
  <PERSON><PERSON>,
  Paperclip,
  Link,
  Menu,
  Plus,
  Edit3,
  MessageSquare,
  Settings,
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import useUserStore from '@/store/userStore';
import { useFileUpload } from '@/hooks/useFileUpload';
import MessageActionPopover from './MessageActionPopover';
import CreateTaskFromMessageModal from './CreateTaskFromMessageModal';
import DeleteMessageModal from './DeleteMessageModal';
import NewSessionModal from './NewSessionModal';
import AttachLinkModal from './AttachLinkModal';
import NextActionsModal from './NextActionsModal';
import ReadStatus from './ReadStatus';
import RichTextEditor from './RichTextEditor';
import { chat<PERSON><PERSON><PERSON><PERSON><PERSON>, assistant<PERSON><PERSON>, chat<PERSON><PERSON> } from '@/services/chatService';
import { toast } from 'react-hot-toast';
import { useSocketEvent } from '@/hooks/useSocket';
import { useAICompletions } from '@/hooks/useAICompletions';
import ChatBotSettings from './ChatBotSettings';
import { formatSmartTimestamp } from '@/utils/dateUtils';
import { replaceMentionsWithHTML } from '@/types/mention';

type RoomType = 'private' | 'task' | 'department' | 'organization';

interface Room {
  id: string;
  name: string;
  type: RoomType;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  isOnline?: boolean;
  avatar?: string;
  isBot?: boolean;
  botDuration?: number;
  chatUsers?: Array<{
    user: {
      id: number;
      firstName: string;
      lastName: string;
      imageUrl?: string;
    };
  }>;
}

interface MessageReadUser {
  id: number;
  firstName: string;
  lastName: string;
  imageUrl?: string;
}

interface MessageReadStatus {
  readByUsers: Array<{
    user: MessageReadUser;
    readAt: string;
  }>;
  unreadUsers: MessageReadUser[];
  totalParticipants: number;
  readCount: number;
  unreadCount: number;
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'sticker' | 'link';
  status: 'sending' | 'delivered' | 'read' | 'failed';
  imageUrl?: string;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    imageUrl?: string;
  };
  readStatus?: MessageReadStatus;
  isRead?: boolean;
  readAt?: string;
}

interface ChatMainProps {
  selectedRoom: Room | null;
  messages: Message[];
  onSendMessage: (content: string, type: 'text' | 'file' | 'image' | 'sticker' | 'link') => void;
  onLoadMoreMessages?: () => void;
  hasMoreMessages?: boolean;
  loadingMore?: boolean;
  currentUser?: {
    id: number;
    firstName: string;
    lastName: string;
  };
  loading?: boolean;
  onMessageDeleted?: (messageId: string) => void;
  onBotSettingsUpdate?: (roomId: string, settings: { isBot: boolean; botDuration: number }) => Promise<void>;
  // Mobile sidebar props
  onMobileSidebarOpen?: () => void;
}

const ChatContainer = styled.div<{ $sidebarOpen: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: row;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const ChatMainContent = styled.div<{ $sidebarOpen: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  @media (min-width: 769px) {
    width: ${props => (props.$sidebarOpen ? 'calc(100% - 320px)' : '100%')};
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const ChatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;
  height: 70px;

  /* Tablet - adjust padding and height */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    height: 65px;
    padding-left: ${appTheme.spacing.sm};
  }

  /* Mobile - add mobile menu button and optimize layout */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: 60px;
    padding: 0 ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.sm};
  }

  /* Small mobile - further optimize */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    height: 56px;
    padding: 0 ${appTheme.spacing.xs};
  }
`;

const ChatHeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  flex: 1;
  min-width: 0;

  /* Mobile - reduce gap and add mobile menu button */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.sm};
  }

  /* Small mobile - minimal gap */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: ${appTheme.spacing.xs};
  }
`;

const ChatHeaderActions = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding-right: ${appTheme.spacing.md};
  flex-shrink: 0;

  /* Tablet - reduce padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding-right: ${appTheme.spacing.sm};
  }

  /* Mobile - remove padding and adjust gap */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding-right: 0;
    gap: ${appTheme.spacing.xs};
  }
`;

// Mobile menu button for sidebar toggle
const MobileMenuButton = styled.button`
  display: none;

  @media (max-width: ${appTheme.breakpoints.md}) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: none;
    color: ${appTheme.colors.text.secondary};
    cursor: pointer;
    border-radius: ${appTheme.borderRadius.sm};
    transition: all 0.2s ease;

    &:hover {
      background: ${appTheme.colors.background.lighter};
      color: ${appTheme.colors.text.primary};
    }

    &:active {
      transform: scale(0.95);
    }
  }
`;

const ActionButton = styled.button`
  padding: ${appTheme.spacing.sm};
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  min-width: 36px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 44px;
    min-height: 44px;
    padding: ${appTheme.spacing.md};

    &:active {
      transform: scale(0.95);
    }
  }
`;

const RoomAvatar = styled.div<{ $type: RoomType }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: ${props => {
    switch (props.$type) {
      case 'private':
        return appTheme.colors.primary;
      case 'task':
        return appTheme.colors.secondary;
      case 'department':
        return '#10b981';
      case 'organization':
        return '#f59e0b';
      default:
        return appTheme.colors.primary;
    }
  }};
  color: white;
  font-size: 14px;
  font-weight: 500;
`;

const ChatTitle = styled.h2`
  font-size: 16px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;

  /* Tablet - slightly smaller font */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    font-size: 15px;
  }

  /* Mobile - smaller font and better truncation */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 14px;
    max-width: 200px;
  }

  /* Small mobile - even smaller */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 13px;
    max-width: 150px;
  }
`;

const ChatSubtitle = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
  margin-top: 2px;
`;

const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing.lg};
  background: linear-gradient(
    135deg,
    ${appTheme.colors.background.light} 0%,
    #f8fafc 50%,
    #f1f5f9 100%
  );
  display: flex;
  flex-direction: column;
  gap: 0; /* Gap is now handled by MessageGroup */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(
      135deg,
      ${appTheme.colors.border} 0%,
      ${appTheme.colors.text.light} 100%
    );
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      135deg,
      ${appTheme.colors.text.light} 0%,
      ${appTheme.colors.text.secondary} 100%
    );
  }

  /* Desktop - optimal padding */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    padding: ${appTheme.spacing.xl} ${appTheme.spacing.xl};
  }

  /* Tablet - balanced padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.lg} ${appTheme.spacing.lg};
  }

  /* Mobile - optimized for touch scrolling */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.lg} ${appTheme.spacing.md};
    max-height: calc(100vh - 200px);

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: ${appTheme.colors.border};
    }
  }

  /* Small mobile - minimal padding */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.md} ${appTheme.spacing.sm};
  }

  /* Extra small mobile - very minimal padding */
  @media (max-width: 360px) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.xs};
  }
`;

const MessageGroup = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.md};
  flex-direction: ${props => (props.$isOwn ? 'row-reverse' : 'row')};
  margin-bottom: ${appTheme.spacing.lg}; /* Increased spacing to accommodate footer */
  padding: 0 ${appTheme.spacing.xs};
  transition: all 0.2s ease;

  /* Enhanced spacing for better visual hierarchy with external footer */
  &:not(:last-child) {
    margin-bottom: ${appTheme.spacing.xl}; /* More space between message groups */
  }

  /* Tablet - adjust spacing */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.sm};
    margin-bottom: ${appTheme.spacing.lg};
    padding: 0 ${appTheme.spacing.xs};

    &:not(:last-child) {
      margin-bottom: ${appTheme.spacing.xl};
    }
  }

  /* Mobile - optimize for touch and readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    gap: ${appTheme.spacing.md};
    margin-bottom: ${appTheme.spacing.xl};
    padding: 0 ${appTheme.spacing.sm};

    &:not(:last-child) {
      margin-bottom: calc(${appTheme.spacing.xl} + ${appTheme.spacing.md}); /* Extra space for footer */
    }
  }

  /* Small mobile - compact but readable */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    gap: ${appTheme.spacing.sm};
    margin-bottom: ${appTheme.spacing.lg};
    padding: 0 ${appTheme.spacing.xs};

    &:not(:last-child) {
      margin-bottom: ${appTheme.spacing.xl};
    }
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 13px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 3px solid white;
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.15),
    0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;

  /* Subtle hover effect */
  &:hover {
    transform: scale(1.05);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 2px 6px rgba(0, 0, 0, 0.15);
  }

  /* Online indicator (can be added later) */
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    right: -1px;
    width: 12px;
    height: 12px;
    background: #10b981;
    border: 2px solid white;
    border-radius: 50%;
    opacity: 0; /* Hidden by default, can be shown based on online status */
    transition: opacity 0.2s ease;
  }

  /* Desktop - optimal size */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    width: 38px;
    height: 38px;
    font-size: 14px;
  }

  /* Tablet - balanced size */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }

  /* Mobile - touch-friendly size */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 40px;
    height: 40px;
    font-size: 14px;
    border-width: 2px;

    &::after {
      width: 14px;
      height: 14px;
    }
  }

  /* Small mobile - maintain good size */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    width: 36px;
    height: 36px;
    font-size: 13px;

    &::after {
      width: 12px;
      height: 12px;
    }
  }

  /* Extra small mobile - minimum functional size */
  @media (max-width: 360px) {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
`;

const MessageContent = styled.div<{ $isOwn: boolean }>`
  max-width: 65%;
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap since footer is now separate */
  min-width: 0; /* Allows proper text wrapping */
  position: relative;

  /* Desktop - optimal reading width */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    max-width: 65%;
  }

  /* Tablet - slightly wider for better use of space */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    max-width: 70%;
  }

  /* Mobile - wider messages for better readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    max-width: 80%;
  }

  /* Small mobile - almost full width but leave space for avatar */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-width: 85%;
  }

  /* Extra small mobile - maximize available space */
  @media (max-width: 360px) {
    max-width: 88%;
  }
`;

const MessageHeader = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.xs};
  justify-content: ${props => (props.$isOwn ? 'flex-end' : 'flex-start')};
  opacity: 0.9;

  /* Mobile - better spacing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    margin-bottom: ${appTheme.spacing.sm};
  }
`;

const SenderName = styled.span`
  font-size: 13px;
  font-weight: 600;
  color: ${appTheme.colors.text.secondary};
  letter-spacing: 0.01em;
  text-rendering: optimizeLegibility;

  /* Mobile - slightly larger for better readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 14px;
  }

  /* Small mobile - maintain readability */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 13px;
  }
`;

const MessageBubble = styled.div<{ $isOwn: boolean }>`
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
  border-radius: ${props => (props.$isOwn ? '20px 20px 4px 20px' : '20px 20px 20px 4px')};
  background: ${props =>
    props.$isOwn
      ? `linear-gradient(135deg, #06c755 0%, #4dc764 100%)`
      : `linear-gradient(135deg, ${appTheme.colors.background.main} 0%, #f8fafc 100%)`};
  color: ${props => (props.$isOwn ? 'white' : appTheme.colors.text.primary)};
  box-shadow: ${props =>
    props.$isOwn
      ? '0 4px 12px rgba(99, 102, 241, 0.25), 0 2px 4px rgba(0, 0, 0, 0.1)'
      : '0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1)'};
  position: relative;
  border: ${props => (props.$isOwn ? 'none' : `1px solid ${appTheme.colors.border}`)};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  backdrop-filter: blur(10px);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;

  /* Enhanced hover effects for desktop */
  @media (min-width: ${appTheme.breakpoints.md}) {
    &:hover {
      transform: translateY(-2px) scale(1.02);
      box-shadow: ${props =>
        props.$isOwn
          ? '0 8px 25px rgba(99, 102, 241, 0.35), 0 4px 12px rgba(0, 0, 0, 0.15)'
          : '0 6px 20px rgba(0, 0, 0, 0.12), 0 3px 8px rgba(0, 0, 0, 0.08)'};

      &::after {
        position: absolute;
        top: 6px;
        ${props => (props.$isOwn ? 'left: 8px;' : 'right: 8px;')}
        font-size: 16px;
        font-weight: bold;
        opacity: 0.9;
        color: ${props =>
          props.$isOwn ? 'rgba(255, 255, 255, 0.95)' : appTheme.colors.text.secondary};
        pointer-events: none;
        animation: fadeInBounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        text-shadow: ${props => (props.$isOwn ? '0 1px 2px rgba(0, 0, 0, 0.2)' : 'none')};
      }
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
      transition: all 0.1s ease;
    }
  }

  @keyframes fadeInBounce {
    0% {
      opacity: 0;
      transform: scale(0.8) rotate(-5deg);
    }
    50% {
      opacity: 1;
      transform: scale(1.1) rotate(0deg);
    }
    100% {
      opacity: 0.9;
      transform: scale(1) rotate(0deg);
    }
  }

  /* Subtle entrance animation */
  animation: slideInMessage 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;

  @keyframes slideInMessage {
    0% {
      opacity: 0;
      transform: translateY(10px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Tablet optimizations */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
    min-height: 50px;
    border-radius: ${props => (props.$isOwn ? '18px 18px 4px 18px' : '18px 18px 18px 4px')};
  }

  /* Mobile - enhanced touch-friendly design */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-height: 52px;
    padding: ${appTheme.spacing.lg} ${appTheme.spacing.xl};
    border-radius: ${props => (props.$isOwn ? '16px 16px 4px 16px' : '16px 16px 16px 4px')};
    box-shadow: ${props =>
      props.$isOwn
        ? '0 3px 10px rgba(99, 102, 241, 0.3), 0 1px 3px rgba(0, 0, 0, 0.12)'
        : '0 2px 6px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)'};

    /* Always show subtle action indicator on mobile */
    &::after {
      position: absolute;
      top: 8px;
      ${props => (props.$isOwn ? 'left: 10px;' : 'right: 10px;')}
      font-size: 14px;
      font-weight: 600;
      opacity: 0.7;
      color: ${props => (props.$isOwn ? 'rgba(255, 255, 255, 0.85)' : appTheme.colors.text.light)};
      pointer-events: none;
      text-shadow: ${props => (props.$isOwn ? '0 1px 1px rgba(0, 0, 0, 0.2)' : 'none')};
    }

    &:active {
      transform: scale(0.97);
      background: ${props =>
        props.$isOwn
          ? `linear-gradient(135deg, ${appTheme.colors.primaryHover} 0%, #5a67d8 100%)`
          : `linear-gradient(135deg, ${appTheme.colors.background.lighter} 0%, #f1f5f9 100%)`};
      box-shadow: ${props =>
        props.$isOwn
          ? '0 2px 6px rgba(99, 102, 241, 0.4), 0 1px 2px rgba(0, 0, 0, 0.15)'
          : '0 1px 4px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.08)'};
      transition: all 0.1s ease;
    }
  }

  /* Small mobile - compact but readable */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
    min-height: 48px;
    border-radius: ${props => (props.$isOwn ? '14px 14px 4px 14px' : '14px 14px 14px 4px')};

    &::after {
      top: 6px;
      ${props => (props.$isOwn ? 'left: 8px;' : 'right: 8px;')}
      font-size: 12px;
    }
  }

  /* Extra small mobile - minimal but functional */
  @media (max-width: 360px) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    min-height: 44px;
    border-radius: ${props => (props.$isOwn ? '12px 12px 4px 12px' : '12px 12px 12px 4px')};
  }
`;

const MessageBubbleContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
  position: relative;
  width: 100%;
  min-width: 0; /* Allows text to wrap properly */
`;

const MessageActions = styled.div<{ $isOwn: boolean }>`
  position: absolute;
  top: -12px;
  ${props => (props.$isOwn ? 'right: -12px;' : 'left: -12px;')}
  z-index: 15;
  opacity: 1;
  transition: all 0.2s ease;

  /* Desktop - better positioning */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    top: -10px;
    ${props => (props.$isOwn ? 'right: -10px;' : 'left: -10px;')}
  }

  /* Mobile - touch-friendly positioning */
  @media (max-width: ${appTheme.breakpoints.md}) {
    top: -8px;
    ${props => (props.$isOwn ? 'right: -8px;' : 'left: -8px;')}
  }
`;

const MessageText = styled.div`
  font-size: 15px;
  line-height: 1.5;
  font-weight: 400;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  letter-spacing: 0.01em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Enhanced readability for links and mentions */
  a {
    color: inherit;
    text-decoration: underline;
    text-decoration-color: rgba(255, 255, 255, 0.6);

    &:hover {
      text-decoration-color: rgba(255, 255, 255, 0.9);
    }
  }

  /* Code snippets styling */
  code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  /* Bold text styling */
  strong, b {
    font-weight: 600;
  }

  /* Mention styling */
  .mention[data-mention="true"] {
    background-color: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  /* Bullet list styling */
  ul {
    margin: 8px 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 4px 0;
    line-height: 1.4;
  }

  /* Prevent deeply nested lists */
  ul ul {
    margin: 4px 0;
    padding-left: 16px;
  }

  /* Desktop - optimal reading size */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    font-size: 15px;
    line-height: 1.5;
  }

  /* Tablet - balanced size */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    font-size: 15px;
    line-height: 1.5;
  }

  /* Mobile - larger font for better readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px;
    line-height: 1.6;
    letter-spacing: 0.02em;
  }

  /* Small mobile - maintain readability */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 15px;
    line-height: 1.5;
  }

  /* Extra small mobile - minimum readable size */
  @media (max-width: 360px) {
    font-size: 14px;
    line-height: 1.4;
  }
`;

// Separate component for received message text with different mention styling
const ReceivedMessageText = styled(MessageText)`
  /* Override mention styling for received messages */
  .mention[data-mention="true"] {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
      font-weight: 600;
    &:hover {
      background-color: #bbdefb;
      border-color: #90caf9;
    }
  }
`;

const MessageFooter = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  margin-top: ${appTheme.spacing.sm};
  justify-content: ${props => (props.$isOwn ? 'flex-end' : 'flex-start')};
  opacity: 0.8;
  transition: opacity 0.2s ease;
  padding: 0 ${appTheme.spacing.xs};

  /* Show footer more prominently on hover */
  &:hover {
    opacity: 1;
  }

  /* Desktop - optimal spacing */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    margin-top: ${appTheme.spacing.sm};
    padding: 0 ${appTheme.spacing.sm};
  }

  /* Tablet - balanced spacing */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    margin-top: ${appTheme.spacing.sm};
    padding: 0 ${appTheme.spacing.xs};
  }

  /* Mobile - better spacing and touch-friendly */
  @media (max-width: ${appTheme.breakpoints.md}) {
    margin-top: ${appTheme.spacing.md};
    gap: ${appTheme.spacing.sm};
    padding: 0 ${appTheme.spacing.sm};
  }

  /* Small mobile - compact but readable */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    margin-top: ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.xs};
    padding: 0 ${appTheme.spacing.xs};
  }
`;

const MessageTime = styled.div<{ $isOwn: boolean }>`
  font-size: 11px;
  font-weight: 500;
  color: ${props => (props.$isOwn ? appTheme.colors.text.light : appTheme.colors.text.light)};
  text-shadow: ${props => (props.$isOwn ? '0 1px 1px rgba(0, 0, 0, 0.2)' : 'none')};
  letter-spacing: 0.02em;
  user-select: none;

  /* Mobile - slightly larger for better readability */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 12px;
  }

  /* Small mobile - maintain readability */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 11px;
  }
`;

const MessageStatus = styled.div<{ $status: string; $isOwn: boolean }>`
  display: ${props => (props.$isOwn ? 'flex' : 'none')};
  align-items: center;
  color: ${props => {
    if (!props.$isOwn) return 'transparent';
    switch (props.$status) {
      case 'sending':
        return 'rgba(255, 255, 255, 0.6)';
      case 'delivered':
        return 'rgba(255, 255, 255, 0.8)';
      case 'read':
        return '#10b981';
      case 'failed':
        return '#ef4444';
      default:
        return 'rgba(255, 255, 255, 0.6)';
    }
  }};
`;

// Loading Skeleton Components
const SkeletonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.lg};
`;

const SkeletonMessage = styled.div<{ $isOwn: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.sm};
  flex-direction: ${props => (props.$isOwn ? 'row-reverse' : 'row')};
  margin-bottom: ${appTheme.spacing.xs};
`;

const SkeletonAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  flex-shrink: 0;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonBubble = styled.div<{ $isOwn: boolean; $width: string }>`
  max-width: 70%;
  width: ${props => props.$width};
  height: 40px;
  border-radius: ${appTheme.borderRadius.lg};
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const LoadingSkeleton = () => {
  const skeletonMessages = [
    { isOwn: false, width: '60%' },
    { isOwn: true, width: '45%' },
    { isOwn: false, width: '75%' },
    { isOwn: false, width: '30%' },
    { isOwn: true, width: '65%' },
  ];

  return (
    <SkeletonContainer>
      {skeletonMessages.map((msg, index) => (
        <SkeletonMessage key={index} $isOwn={msg.isOwn}>
          {!msg.isOwn && <SkeletonAvatar />}
          <SkeletonBubble $isOwn={msg.isOwn} $width={msg.width} />
        </SkeletonMessage>
      ))}
    </SkeletonContainer>
  );
};

const LoadMoreContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.sm};
`;

const LoadMoreSpinner = styled.div`
  width: 20px;
  height: 20px;
  border: 2px solid ${appTheme.colors.border};
  border-top: 2px solid ${appTheme.colors.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const LoadMoreSkeleton = () => {
  return (
    <LoadMoreContainer>
      <LoadMoreSpinner />
    </LoadMoreContainer>
  );
};

const MessageInput = styled.div`
  padding: ${appTheme.spacing.md};
  border-top: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
  backdrop-filter: blur(10px);
  position: sticky;
  bottom: 0;
  z-index: 10;

  /* Tablet - adjust padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  }

  /* Mobile - optimize for touch and keyboard */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm};
    background: ${appTheme.colors.background.main};
    border-top: 2px solid ${appTheme.colors.border};

    /* Ensure input area is above mobile keyboards */
    position: fixed;
    bottom: 70px;
    left: 0;
    right: 0;
    z-index: 100;
  }

  /* Small mobile - minimal padding */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  }
`;

const InputContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.sm};
  border: 2px solid ${appTheme.colors.border};
  border-radius: 20px;
  background: white;
  transition: all 0.2s ease;
  box-shadow: ${appTheme.shadows.sm};
  min-height: 48px;

  &:focus-within {
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  /* Mobile - touch-friendly sizing and spacing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-height: 52px;
    padding: ${appTheme.spacing.md};
    gap: ${appTheme.spacing.md};
    border-radius: 24px;

    &:focus-within {
      box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15);
    }
  }

  /* Small mobile - optimize spacing */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-height: 48px;
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    gap: ${appTheme.spacing.sm};
  }
`;


const InputButton = styled.button`
  padding: ${appTheme.spacing.xs};
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 44px;
    min-height: 44px;
    padding: ${appTheme.spacing.sm};
    border-radius: ${appTheme.borderRadius.md};

    &:active {
      transform: scale(0.95);
      background: ${appTheme.colors.background.lighter};
    }
  }

  /* Small mobile - slightly smaller but still touch-friendly */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-width: 40px;
    min-height: 40px;
  }
`;

const SendButton = styled.button`
  padding: ${appTheme.spacing.sm};
  border: none;
  background: ${appTheme.colors.primary};
  color: white;
  border-radius: ${appTheme.borderRadius.lg};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: ${appTheme.shadows.sm};
  min-width: 40px;
  min-height: 40px;

  &:hover {
    background: ${appTheme.colors.primaryHover};
    transform: translateY(-1px);
    box-shadow: ${appTheme.shadows.md};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: ${appTheme.shadows.sm};
  }

  /* Mobile - larger touch target and better feedback */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 48px;
    min-height: 48px;
    padding: ${appTheme.spacing.md};
    border-radius: 50%;

    &:hover {
      transform: none; /* Remove hover transform on mobile */
    }

    &:active {
      transform: scale(0.95);
      background: ${appTheme.colors.primaryHover};
    }
  }

  /* Small mobile - maintain good touch target */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-width: 44px;
    min-height: 44px;
  }
`;

const EmptyState = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.secondary};
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.light};
`;

// Image upload styled components
const ImagePreviewContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.sm};
  padding: 0 ${appTheme.spacing.sm};
`;

const ImagePreview = styled.div`
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: ${appTheme.borderRadius.md};
  border: 2px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.lighter};
`;

const PreviewImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: ${appTheme.borderRadius.md};
`;

const RemoveImageButton = styled.button`
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: none;
  background: ${appTheme.colors.error.main};
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  box-shadow: ${appTheme.shadows.sm};
  transition: all 0.2s ease;

  &:hover {
    background: #dc2626;
    transform: scale(1.1);
  }
`;

const ImageUploadButton = styled.label`
  padding: ${appTheme.spacing.xs};
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.primary};
  }

  input {
    display: none;
  }
`;

const UploadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  color: ${appTheme.colors.text.secondary};
  font-size: 12px;
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const MessageImage = styled.img`
  max-width: 320px;
  max-height: 240px;
  border-radius: ${appTheme.borderRadius.lg};
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  background: ${appTheme.colors.background.lighter};

  &:hover {
    transform: scale(1.03);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.2),
      0 4px 8px rgba(0, 0, 0, 0.15);
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Desktop - optimal size */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    max-width: 320px;
    max-height: 240px;
  }

  /* Tablet - balanced size */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    max-width: 280px;
    max-height: 210px;
  }

  /* Mobile - responsive sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    max-width: 260px;
    max-height: 195px;
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile - compact size */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-width: 220px;
    max-height: 165px;
  }

  /* Extra small mobile - minimal size */
  @media (max-width: 360px) {
    max-width: 180px;
    max-height: 135px;
  }
`;

const MessageLink = styled.a`
  color: ${props =>
    props.color === 'white' ? 'rgba(255, 255, 255, 0.9)' : appTheme.colors.primary};
  text-decoration: none;
  word-break: break-all;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  padding: ${appTheme.spacing.xs} 0;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }

  &:visited {
    color: ${props =>
      props.color === 'white' ? 'rgba(255, 255, 255, 0.7)' : appTheme.colors.primaryHover};
  }
`;

const ImagePreviewModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const PreviewImageContainer = styled.div`
  position: relative;
  width: 95vw;
  height: 95vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
`;

const FullScreenImage = styled.img<{ $zoom?: number }>`
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: ${appTheme.borderRadius.md};
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease;
  cursor: ${props => ((props.$zoom || 1) === 1 ? 'zoom-in' : 'zoom-out')};
  transform: scale(${props => props.$zoom || 1});
  user-select: none;

  &:hover {
    filter: brightness(1.05);
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
`;

const ZoomControls = styled.div`
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ZoomButton = styled.button`
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 18px;
  font-weight: bold;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ZoomIndicator = styled.div`
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
`;

// Right Sidebar Components
const RightSidebar = styled.div<{ $isOpen: boolean }>`
  width: 320px;
  background: ${appTheme.colors.background.light};
  border-left: 1px solid ${appTheme.colors.border};
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  flex-direction: column;
  flex-shrink: 0;
  height: 100%;
  box-shadow: ${appTheme.shadows.lg};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  /* Desktop (1024px+) - maintain current layout */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    width: 320px;
  }

  /* Tablet (768px - 1023px) - slightly smaller width */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    width: 280px;
  }

  /* Mobile (< 768px) - overlay behavior with smooth animations */
  @media (max-width: ${appTheme.breakpoints.md}) {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: calc(100vh - 70px);
    z-index: 1000;
    background: ${appTheme.colors.background.main};
    border-left: 1px solid ${appTheme.colors.border};
    box-shadow: ${appTheme.shadows.lg};
    transform: translateX(${props => (props.$isOpen ? '0' : '100%')});
    display: flex;
    backdrop-filter: blur(10px);
  }

  /* Small mobile (< 480px) - full width overlay */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    width: 100%;
    border-left: none;
    box-shadow: none;
  }
`;

const SidebarHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
  height: 70px;
  flex-shrink: 0;

  /* Tablet - adjust height and padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    height: 65px;
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  }

  /* Mobile - optimize for touch and add close button */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: 60px;
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    background: ${appTheme.colors.background.main};
    border-bottom: 2px solid ${appTheme.colors.border};
  }

  /* Small mobile - minimal height */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    height: 56px;
    padding: ${appTheme.spacing.sm};
  }
`;

const SidebarTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Mobile - smaller font */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 15px;
  }

  /* Small mobile - even smaller */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    font-size: 14px;
  }
`;

const SidebarCloseButton = styled.button`
  padding: ${appTheme.spacing.xs};
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 44px;
    min-height: 44px;
    padding: ${appTheme.spacing.sm};

    &:active {
      transform: scale(0.95);
      background: ${appTheme.colors.background.lighter};
    }
  }

  /* Small mobile - slightly smaller but still touch-friendly */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-width: 40px;
    min-height: 40px;
  }
`;

const SidebarContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }

  /* Tablet - adjust padding */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  }

  /* Mobile - optimize padding and scrolling */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.sm};

    &::-webkit-scrollbar {
      width: 4px;
    }
  }

  /* Small mobile - minimal padding */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  }
`;

const UserListItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.sm};
  border-radius: ${appTheme.borderRadius.md};
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 48px;

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-height: 52px;
    padding: ${appTheme.spacing.md};
    gap: ${appTheme.spacing.md};

    &:active {
      transform: scale(0.98);
      background: ${appTheme.colors.background.lighter};
    }
  }

  /* Small mobile - optimize spacing */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-height: 48px;
    padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
    gap: ${appTheme.spacing.sm};
  }
`;

const SidebarUserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : appTheme.colors.primary};
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  box-shadow: ${appTheme.shadows.sm};
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 2px;
`;

const UserStatus = styled.div<{ $isOnline?: boolean }>`
  font-size: 12px;
  color: ${props => (props.$isOnline ? '#10b981' : appTheme.colors.text.secondary)};
  display: flex;
  align-items: center;
  gap: 4px;

  &::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: ${props => (props.$isOnline ? '#10b981' : appTheme.colors.text.light)};
  }
`;

const AIAssistantPanel = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
`;

const AIAssistantCard = styled.div`
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.md};
  box-shadow: ${appTheme.shadows.sm};
`;

const AIAssistantHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.sm};
`;

const AIAssistantIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
`;

const AIAssistantTitle = styled.h4`
  font-size: 14px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const AIAssistantDescription = styled.p`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
  margin: 0;
  line-height: 1.4;
`;

const AssistantMessagesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
  max-height: calc(100% - 100px);
  overflow-y: auto;
  padding: ${appTheme.spacing.md};
`;

const AssistantMessage = styled.div<{ $isUser?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: ${props => (props.$isUser ? 'flex-end' : 'flex-start')};
  margin-bottom: ${appTheme.spacing.sm};
`;

const AssistantMessageBubble = styled.div<{ $isUser?: boolean; $isAction?: boolean }>`
  max-width: ${props => (props.$isAction ? '100%' : '90%')};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.lg};
  background: ${props => {
    if (props.$isAction) {
      return appTheme.colors.text.light; // Gray background for Action messages
    }
    return props.$isUser ? appTheme.colors.primary : appTheme.colors.background.main;
  }};
  color: ${props => {
    if (props.$isAction) {
      return 'white'; // White text on gray background for Action messages
    }
    return props.$isUser ? 'white' : appTheme.colors.text.primary;
  }};
  border: ${props => {
    if (props.$isAction) {
      return `none`; // Darker gray border for Action messages
    }
    return props.$isUser ? 'none' : `1px solid ${appTheme.colors.border}`;
  }};
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  transform: ${props =>
    props.$isAction ? 'scale(0.8)' : 'scale(1)'}; // Reduced size for Action messages
  transform-origin: left center; // Scale from left to maintain positioning
  margin-top: ${props => (props.$isAction ? '-20px' : '0')};
  cursor: ${props => (props.$isAction ? 'pointer' : 'default')};
`;

const AssistantMessageTime = styled.div`
  font-size: 11px;
  color: ${appTheme.colors.text.light};
  margin-top: ${appTheme.spacing.xs};
`;

const NextActionsButton = styled.button`
  margin-top: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  background: gray;
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.xs};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};

  &:hover {
    background: ${appTheme.colors.primaryHover};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const AssistantMessageActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.xs};
  margin-top: ${appTheme.spacing.xs};
  align-items: center;
`;

const MessageActionButton = styled.button`
  padding: ${appTheme.spacing.xs};
  background: transparent;
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  color: ${appTheme.colors.text.light};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.xs};

  &:hover {
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
    border-color: ${appTheme.colors.text.light};
  }

  &:active {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AssistantLoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.secondary};
`;

const AssistantEmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  text-align: center;
  color: ${appTheme.colors.text.secondary};

  h4 {
    margin: ${appTheme.spacing.sm} 0;
    color: ${appTheme.colors.text.primary};
  }

  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
  }
`;

const AssistantChatUserSelector = styled.div`
  padding: ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.lighter};
`;

const AssistantChatUserSelect = styled.select`
  width: 100%;
  padding: ${appTheme.spacing.sm};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  font-size: 14px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: ${appTheme.colors.background.lighter};
  }
`;

const AssistantChatUserLabel = styled.label`
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: ${appTheme.colors.text.secondary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const AssistantChatUserHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${appTheme.spacing.xs};
`;

const NewSessionButton = styled.button`
  padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
  border: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: ${appTheme.colors.background.lighter};
    border-color: ${appTheme.colors.primary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SidebarToggleButton = styled.button<{ $isActive?: boolean; $disabled?: boolean }>`
  padding: ${appTheme.spacing.sm};
  border: none;
  background: ${props => (props.$isActive ? appTheme.colors.primaryLight : 'none')};
  color: ${props =>
    props.$disabled
      ? appTheme.colors.text.light
      : props.$isActive
        ? appTheme.colors.primary
        : appTheme.colors.text.secondary};
  cursor: ${props => (props.$disabled ? 'not-allowed' : 'pointer')};
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: ${props => (props.$disabled ? 0.5 : 1)};

  &:hover {
    background: ${props =>
      props.$disabled
        ? 'none'
        : props.$isActive
          ? appTheme.colors.primaryLight
          : appTheme.colors.background.lighter};
    color: ${props =>
      props.$disabled
        ? appTheme.colors.text.light
        : props.$isActive
          ? appTheme.colors.primary
          : appTheme.colors.text.primary};
  }

  &:active {
    transform: ${props => (props.$disabled ? 'none' : 'scale(0.95)')};
  }
`;

const MobileSidebarOverlay = styled.div<{ $isVisible: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 15;
  display: ${props => (props.$isVisible ? 'block' : 'none')};
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;

  @media (min-width: 769px) {
    display: none;
  }
`;

export default function ChatMain({
  selectedRoom,
  messages,
  onSendMessage,
  onLoadMoreMessages,
  hasMoreMessages = false,
  loadingMore = false,
  currentUser,
  loading = false,
  onMessageDeleted,
  onBotSettingsUpdate,
  onMobileSidebarOpen,
}: ChatMainProps) {
  const [messageText, setMessageText] = useState('');
  const [messageHtml, setMessageHtml] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [imageZoom, setImageZoom] = useState(1);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(false);
  const [sidebarContent, setSidebarContent] = useState<'users' | 'ai' | 'settings'>('users');
  const [currentAssistantChatUser, setCurrentAssistantChatUser] = useState<any>(null);
  const [showNewSessionModal, setShowNewSessionModal] = useState(false);
  const [assistantChatUsers, setAssistantChatUsers] = useState<any[]>([]);
  const [assistantMessages, setAssistantMessages] = useState<any[]>([]);
  const [assistantMessageTypes, setAssistantMessageTypes] = useState<any[]>([]);
  const [assistantLoading, setAssistantLoading] = useState(false);
  const [chatUsersLoading, setChatUsersLoading] = useState(false);
  const [showNextActionsModal, setShowNextActionsModal] = useState(false);
  const [botSettingsLoading, setBotSettingsLoading] = useState(false);
  const [currentNextActions, setCurrentNextActions] = useState<string[]>([]);
  const [currentAssistantContent, setCurrentAssistantContent] = useState<string>('');
  const { userData } = useUserStore();
  const assistantMessagesRef = useRef<HTMLDivElement>(null);
  const { generateChatText, loading: aiLoading, error: aiError } = useAICompletions();
  const { uploadFile, isUploading } = useFileUpload();

  // Action menu state
  const [activeMessageId, setActiveMessageId] = useState<string | null>(null);

  // Helper function to process message content for mentions
  const processMessageContent = (content: string): string => {
    // If content already contains HTML tags, assume it's already processed
    if (content.includes('<')) {
      return content;
    }
    // Convert mention format to HTML
    return replaceMentionsWithHTML(content);
  };

  // Modal states
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showAttachLinkModal, setShowAttachLinkModal] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(0);
  const isLoadingMoreRef = useRef(false);
  const scrollPositionRef = useRef(0);
  const prevScrollHeightRef = useRef(0);

  // Auto scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Track loading more state
  useEffect(() => {
    isLoadingMoreRef.current = loadingMore;
  }, [loadingMore]);

  // Enhanced auto-scroll logic
  useEffect(() => {
    const prevLength = prevMessagesLengthRef.current;
    const currentLength = messages.length;
    const container = messagesContainerRef.current;

    // Case 1: Pagination - maintain scroll position
    if (isLoadingMoreRef.current && currentLength > prevLength && prevScrollHeightRef.current > 0) {
      if (container) {
        // Calculate the height difference from new messages loaded at the top
        const newScrollHeight = container.scrollHeight;
        const heightDifference = newScrollHeight - prevScrollHeightRef.current;

        // Adjust scroll position to maintain visual position
        // Use setTimeout to ensure DOM has updated
        setTimeout(() => {
          container.scrollTop = scrollPositionRef.current + heightDifference;
        }, 0);

        // Reset the saved scroll height
        prevScrollHeightRef.current = 0;
      }
      // Update previous length and return early
      prevMessagesLengthRef.current = currentLength;
      return;
    }

    // Case 2: Initial load - always scroll to bottom
    if (prevLength === 0 && currentLength > 0) {
      setTimeout(() => {
        scrollToBottom();
      }, 100); // Small delay to ensure DOM is fully rendered
      prevMessagesLengthRef.current = currentLength;
      return;
    }

    // Case 3: New messages added (not from pagination)
    if (currentLength > prevLength && !isLoadingMoreRef.current) {
      // Check if the new message is from the current user
      const lastMessage = messages[messages.length - 1];
      const isOwnMessage = userData ? String(lastMessage?.senderId) === String(userData.id) : false;

      if (isOwnMessage) {
        // Always scroll to bottom for our own messages
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      } else {
        // For other users' messages, only scroll if user is near the bottom
        if (container) {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100; // 100px threshold

          if (isNearBottom) {
            setTimeout(() => {
              scrollToBottom();
            }, 100);
          }
        }
      }
    }

    // Update previous length
    prevMessagesLengthRef.current = currentLength;
  }, [messages, loadingMore, userData]);

  // Reset tracking when room changes
  useEffect(() => {
    prevMessagesLengthRef.current = 0;
    isLoadingMoreRef.current = false;
    scrollPositionRef.current = 0;
    prevScrollHeightRef.current = 0;
  }, [selectedRoom]);

  // Auto resize textarea
  useEffect(() => {
    const textarea = document.querySelector('textarea');
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, [messageText]);

  // Handle scroll to load more messages
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container || !onLoadMoreMessages) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtTop = scrollTop === 0;

      // Load more messages when scrolled to top and there are more messages to load
      if (isAtTop && hasMoreMessages && !loadingMore && !loading) {
        // Save current scroll position and height before loading
        scrollPositionRef.current = scrollTop;
        prevScrollHeightRef.current = scrollHeight;
        onLoadMoreMessages();
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onLoadMoreMessages, hasMoreMessages, loadingMore, loading]);

  // Cleanup image preview URLs on unmount
  useEffect(() => {
    return () => {
      imagePreviewUrls.forEach(url => URL.revokeObjectURL(url));
    };
  }, []);

  // Auto-mark messages as read when they come into view
  useEffect(() => {
    if (!selectedRoom || !messages.length || !userData) return;

    const container = messagesContainerRef.current;
    if (!container) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const messageElement = entry.target as HTMLElement;
            const messageId = messageElement.getAttribute('data-message-id');
            const senderId = messageElement.getAttribute('data-sender-id');

            // Only mark messages from other users as read
            if (messageId && senderId && senderId !== userData.id?.toString()) {
              // Import chatMessageApi dynamically to avoid circular imports
              import('@/services/chatService').then(({ chatMessageApi }) => {
                chatMessageApi.markMessageAsRead(parseInt(messageId)).catch(error => {
                  console.error('Error marking message as read:', error);
                });
              });
            }
          }
        });
      },
      {
        root: container,
        rootMargin: '0px',
        threshold: 0.5, // Mark as read when 50% of message is visible
      }
    );

    // Observe all message elements
    const messageElements = container.querySelectorAll('.message-group');
    messageElements.forEach(element => observer.observe(element));

    return () => {
      observer.disconnect();
    };
  }, [selectedRoom, messages, userData]);

  // Handle keyboard events for image preview modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && previewImage) {
        closeImagePreview();
      }
    };

    if (previewImage) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [previewImage]);

  // Image handling functions
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) return;

    // Create preview URLs
    const newPreviewUrls = imageFiles.map(file => URL.createObjectURL(file));

    setSelectedImages(prev => [...prev, ...imageFiles]);
    setImagePreviewUrls(prev => [...prev, ...newPreviewUrls]);

    // Reset input
    event.target.value = '';
  };

  const removeImage = (index: number) => {
    // Revoke the object URL to free memory
    URL.revokeObjectURL(imagePreviewUrls[index]);

    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    const hasText = messageText.trim();
    const hasImages = selectedImages.length > 0;

    if (!hasText && !hasImages) return;

    try {
      if (hasImages) {
        // Upload images first
        for (const image of selectedImages) {
          const uploadResult = await uploadFile(image, 'chat');
          if (uploadResult.success && uploadResult.data && uploadResult.data.url) {
            // If image upload successful and URL is available, set messageType to "image"
            const imageUrl = uploadResult.data.url;
            onSendMessage(imageUrl, 'image');
          } else {
            console.error('Failed to upload image:', uploadResult.error || 'Unknown error');
          }
        }

        // Clear images after upload
        imagePreviewUrls.forEach(url => URL.revokeObjectURL(url));
        setSelectedImages([]);
        setImagePreviewUrls([]);
      }

      if (hasText) {
        // Send text message with HTML formatting
        onSendMessage(messageHtml || messageText.trim(), 'text');
        setMessageText('');
        setMessageHtml('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const getRoomIcon = (type: RoomType) => {
    switch (type) {
      case 'private':
        return <MessageCircle size={16} />;
      case 'task':
        return <CheckSquare size={16} />;
      case 'department':
        return <Users size={16} />;
      case 'organization':
        return <Building2 size={16} />;
      default:
        return <MessageCircle size={16} />;
    }
  };

  const getRoomPrefix = (type: RoomType) => {
    switch (type) {
      case 'private':
        return '';
      case 'task':
        return '#';
      case 'department':
        return '#';
      case 'organization':
        return '#';
      default:
        return '';
    }
  };

  const formatTime = (timeString: string) => {
    return formatSmartTimestamp(timeString);
  };

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'read':
        return <CheckCheck size={12} />;
      default:
        return null; // Don't show icon for other statuses
    }
  };

  const getUserInitials = (firstName?: string, lastName?: string) => {
    if (!firstName && !lastName) return '?';
    const first = firstName ? firstName.charAt(0).toUpperCase() : '';
    const last = lastName ? lastName.charAt(0).toUpperCase() : '';
    return first + last || '?';
  };

  const shouldShowUserImage = (imageUrl?: string | null) => {
    return imageUrl && imageUrl.trim() !== '' && imageUrl !== 'null' && imageUrl !== 'undefined';
  };

  const getDisplayName = (message: Message) => {
    if (message.user) {
      return `${message.user.firstName} ${message.user.lastName}`;
    }
    return message.senderName;
  };

  const getRoomDisplayName = (room: Room) => {
    if (room.type === 'private' && room.chatUsers && currentUser) {
      // Filter out current user and show other users' names
      const otherUsers = room.chatUsers.filter(cu => cu.user.id !== currentUser.id);
      if (otherUsers.length > 0) {
        return otherUsers.map(cu => `${cu.user.firstName} ${cu.user.lastName}`).join(', ');
      }
      return 'Private Chat';
    }
    return room.name;
  };

  const handleImagePreview = (imageUrl: string) => {
    setPreviewImage(imageUrl);
    setImageZoom(1); // Reset zoom when opening new image
  };

  const closeImagePreview = () => {
    setPreviewImage(null);
    setImageZoom(1);
  };

  const handleImageClick = () => {
    setImageZoom(prev => (prev === 1 ? 2 : 1)); // Toggle between 1x and 2x zoom
  };

  const zoomIn = () => {
    setImageZoom(prev => Math.min(3, prev + 0.25));
  };

  const zoomOut = () => {
    setImageZoom(prev => Math.max(0.5, prev - 0.25));
  };

  const resetZoom = () => {
    setImageZoom(1);
  };

  // Sidebar toggle functions
  const toggleUsersSidebar = () => {
    if (rightSidebarOpen && sidebarContent === 'users') {
      setRightSidebarOpen(false);
    } else {
      setSidebarContent('users');
      setRightSidebarOpen(true);
    }
  };

  const toggleAISidebar = async () => {
    if (rightSidebarOpen && sidebarContent === 'ai') {
      setRightSidebarOpen(false);
    } else {
      setSidebarContent('ai');
      setRightSidebarOpen(true);
    }
  };

  const toggleSettingsSidebar = () => {
    if (rightSidebarOpen && sidebarContent === 'settings') {
      setRightSidebarOpen(false);
    } else {
      setSidebarContent('settings');
      setRightSidebarOpen(true);
    }
  };

  const handleNewSessionGenerate = async (userMessageContent: string) => {
    if (!selectedRoom || !userData || !userMessageContent.trim()) return;

    setAssistantLoading(true);
    setShowNewSessionModal(false);

    try {
      await createAssistantChatUserAndGenerate(userMessageContent.trim());
    } catch (error) {
      console.error('Error in new session generation:', error);
      toast.error('Failed to create new AI assistant session');
    } finally {
      setAssistantLoading(false);
    }
  };

  const createAssistantChatUserAndGenerate = async (userMessageContent: string) => {
    if (!selectedRoom || !userData) return;

    console.log('🚀 Starting AI assistant workflow...');
    console.log('📝 Creating new assistant_chat_user with name:', userMessageContent);

    const createResponse = await assistantApi.createAssistantChatUser({
      name: userMessageContent,
      chatId: parseInt(selectedRoom.id),
      isActive: true,
    });

    if (!createResponse || !createResponse.assistantChatUser) {
      throw new Error('Assistant chat user creation failed - no response data');
    }

    const assistantChatUser = createResponse.assistantChatUser;
    if (!assistantChatUser.id || typeof assistantChatUser.id !== 'number') {
      throw new Error(`Assistant chat user creation failed - invalid ID: ${assistantChatUser.id}`);
    }

    console.log('✅ Assistant chat user created successfully with ID:', assistantChatUser.id);
    setCurrentAssistantChatUser(assistantChatUser);

    // Fetch assistant message types (required for message creation)
    console.log('📋 Fetching assistant message types...');
    await fetchAssistantMessageTypes();

    // Create user message record using the newly created assistant_chat_user ID
    console.log('💬 Creating user message with assistant_chat_user ID:', assistantChatUser.id);
    const userMessage = await createUserMessage(assistantChatUser.id, userMessageContent);
    console.log('✅ User message created:', userMessage?.id);

    console.log('🤖 Generating AI response with assistant_chat_user ID:', assistantChatUser.id);
    const aiMessage = await generateAndSaveAIResponse(assistantChatUser.id, userMessageContent);
    console.log('✅ AI response created:', aiMessage?.id);

    console.log('📨 Fetching all messages for assistant_chat_user ID:', assistantChatUser.id);
    await fetchAssistantMessages(assistantChatUser.id);

    console.log('🔄 Refreshing assistant chat users list...');
    await fetchAssistantChatUsers();

    setSidebarContent('ai');
    setRightSidebarOpen(true);

    console.log('🎉 AI assistant workflow completed successfully!');
    toast.success(`AI conversation "${userMessageContent}" started!`);
  };

  const handleCreateAssistantChatUser = async () => {
    if (!selectedRoom || !userData || !messageText.trim()) return;

    const userMessageContent = messageText.trim();
    setAssistantLoading(true);

    try {
      // Step 1: ALWAYS create a new assistant_chat_user record first
      // Each AI interaction creates a fresh session with the message text as name
      console.log('🚀 Starting AI assistant workflow...');
      console.log('📝 Creating new assistant_chat_user with name:', userMessageContent);

      const createResponse = await assistantApi.createAssistantChatUser({
        name: userMessageContent,
        chatId: parseInt(selectedRoom.id),
        isActive: true,
      });

      // Step 2: Validate the response and ensure we have a valid ID
      if (!createResponse || !createResponse.assistantChatUser) {
        throw new Error('Assistant chat user creation failed - no response data');
      }

      const assistantChatUser = createResponse.assistantChatUser;
      if (!assistantChatUser.id || typeof assistantChatUser.id !== 'number') {
        throw new Error(
          `Assistant chat user creation failed - invalid ID: ${assistantChatUser.id}`
        );
      }

      console.log('✅ Assistant chat user created successfully with ID:', assistantChatUser.id);
      setCurrentAssistantChatUser(assistantChatUser);

      // Step 3: Fetch assistant message types (required for message creation)
      console.log('📋 Fetching assistant message types...');
      await fetchAssistantMessageTypes();

      // Step 4: Create user message record using the newly created assistant_chat_user ID
      console.log('💬 Creating user message with assistant_chat_user ID:', assistantChatUser.id);
      const userMessage = await createUserMessage(assistantChatUser.id, userMessageContent);
      console.log('✅ User message created:', userMessage?.id);

      // Step 5: Generate and save AI response using the same assistant_chat_user ID
      console.log('🤖 Generating AI response with assistant_chat_user ID:', assistantChatUser.id);
      const aiMessage = await generateAndSaveAIResponse(assistantChatUser.id, userMessageContent);
      console.log('✅ AI response created:', aiMessage?.id);

      // Step 6: Fetch and display all messages for this assistant_chat_user
      console.log('📨 Fetching all messages for assistant_chat_user ID:', assistantChatUser.id);
      await fetchAssistantMessages(assistantChatUser.id);

      // Step 7: Refresh the chat users list to show the new session
      console.log('🔄 Refreshing assistant chat users list...');
      await fetchAssistantChatUsers();

      // Step 8: Clear message input and update UI state
      setMessageText('');
      setSidebarContent('ai');
      setRightSidebarOpen(true);

      console.log('🎉 AI assistant workflow completed successfully!');
      toast.success(`AI conversation "${userMessageContent}" started!`);
    } catch (error) {
      console.error('❌ Error in AI assistant workflow:', error);

      // Provide specific error messages based on the failure point
      let errorMessage = 'Failed to process AI assistant request';
      const errorMsg = error instanceof Error ? error.message : String(error);
      if (errorMsg.includes('Assistant chat user creation failed')) {
        errorMessage = 'Failed to create AI conversation session';
      } else if (errorMsg.includes('user message')) {
        errorMessage = 'Failed to save your message';
      } else if (errorMsg.includes('AI response')) {
        errorMessage = 'Failed to generate AI response';
      }

      toast.error(errorMessage);

      // Reset UI state on error
      setCurrentAssistantChatUser(null);
    } finally {
      setAssistantLoading(false);
    }
  };

  const scrollAssistantToBottom = () => {
    if (assistantMessagesRef.current) {
      assistantMessagesRef.current.scrollTop = assistantMessagesRef.current.scrollHeight;
    }
  };

  const fetchAssistantMessages = async (assistantChatUserId: number) => {
    try {
      const response = await assistantApi.getAssistantMessages(assistantChatUserId, {
        limit: 50,
        order: 'asc', // Order messages chronologically (oldest first, latest at bottom)
      });
      setAssistantMessages(response.assistantMessages || []);

      // Auto-scroll to bottom after messages are loaded
      setTimeout(scrollAssistantToBottom, 100);
    } catch (error) {
      console.error('Error fetching assistant messages:', error);
      toast.error('Failed to load assistant messages');
    }
  };

  const fetchAssistantMessageTypes = async () => {
    try {
      const response = await assistantApi.getMessageTypes();
      setAssistantMessageTypes(response.assistantMessageTypes || []);
    } catch (error) {
      console.error('Error fetching assistant message types:', error);
      toast.error('Failed to load message types');
    }
  };

  const fetchAssistantChatUsers = async () => {
    if (!selectedRoom || !userData) return;

    setChatUsersLoading(true);
    try {
      const response = await assistantApi.getAssistantChatUsers({
        userId: userData.id,
        chatId: parseInt(selectedRoom.id),
        isActive: true,
      });

      // Sort by most recent first (updatedAt DESC, then createdAt DESC)
      const sortedChatUsers = (response.assistantChatUsers || []).sort((a: any, b: any) => {
        const aTime = new Date(a.updatedAt || a.createdAt).getTime();
        const bTime = new Date(b.updatedAt || b.createdAt).getTime();
        return bTime - aTime; // Most recent first
      });

      setAssistantChatUsers(sortedChatUsers);
    } catch (error) {
      console.error('Error fetching assistant chat users:', error);
      toast.error('Failed to load assistant chat sessions');
    } finally {
      setChatUsersLoading(false);
    }
  };

  const handleAssistantChatUserChange = async (assistantChatUserId: string) => {
    if (!assistantChatUserId) return;

    const selectedChatUser = assistantChatUsers.find(
      user => user.id.toString() === assistantChatUserId
    );
    if (!selectedChatUser) return;

    setAssistantLoading(true);
    try {
      setCurrentAssistantChatUser(selectedChatUser);
      await fetchAssistantMessages(selectedChatUser.id);
    } catch (error) {
      console.error('Error switching assistant chat user:', error);
      toast.error('Failed to switch assistant chat session');
    } finally {
      setAssistantLoading(false);
    }
  };

  const handleBotSettingsUpdate = async (settings: { isBot: boolean; botDuration: number }) => {
    if (!selectedRoom || !onBotSettingsUpdate) return;

    setBotSettingsLoading(true);
    try {
      await onBotSettingsUpdate(selectedRoom.id, settings);
      toast.success('Bot settings updated successfully');
    } catch (error) {
      console.error('Error updating bot settings:', error);
      toast.error('Failed to update bot settings');
      throw error; // Re-throw to allow component to handle revert
    } finally {
      setBotSettingsLoading(false);
    }
  };

  const createUserMessage = async (assistantChatUserId: number, content: string) => {
    try {
      console.log('📝 Creating user message for assistant_chat_user ID:', assistantChatUserId);
      const response = await assistantApi.createAssistantMessage({
        assistantChatUserId,
        assistantMessageTypeId: 2, // User type
        content: content.trim(),
      });

      if (!response || !response.assistantMessage) {
        throw new Error('Failed to create user message - no response data');
      }

      console.log('✅ User message created successfully with ID:', response.assistantMessage.id);
      return response.assistantMessage;
    } catch (error) {
      console.error('❌ Error creating user message:', error);
      const errorMsg =
        error instanceof Error ? error.message : 'Unknown error creating user message';
      throw new Error(`Failed to create user message: ${errorMsg}`);
    }
  };

  const generateAndSaveAIResponse = async (assistantChatUserId: number, userMessage: string) => {
    try {
      console.log('🤖 Generating AI response for assistant_chat_user ID:', assistantChatUserId);

      // Fixed system prompt as specified
      const systemPrompt =
        'ให้ response ออกมาเป็น JSON มี Key "content" คือ ขยายความจะข้อความที่ส่งมาให้ละเอียดเเละเข้าใจง่ายมากขึ้น และ Key "next_actions" คือ ให้สร้าง prompt สั้นๆ ที่บ่งบอกว่า user ควรทำอะไรต่อในขั้นตอนต่อไป เพื่อทำให้ความต้องการสมบูรณ์มากขึ้น มา 5 prompt เป็น list array';

      // Generate AI response using the chat completion
      console.log('🔄 Calling AI service...');
      const aiResponse = await generateChatText(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userMessage },
        ],
        {
          model: 'openai/gpt-4o',
          max_tokens: 500,
          temperature: 0.7,
        }
      );

      if (!aiResponse || aiResponse.trim().length === 0) {
        throw new Error('No response from AI service or empty response');
      }

      console.log('✅ AI response generated, parsing JSON structure...');
      console.log('🔍 Raw AI response:', aiResponse);

      // Clean the AI response by removing ```json wrapper if present
      let cleanedResponse = aiResponse.trim();
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 Removed ```json wrapper from response');
      }
      console.log('🔍 Cleaned AI response:', cleanedResponse);

      // Parse the JSON response
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(cleanedResponse);
        console.log('✅ Successfully parsed JSON:', parsedResponse);
      } catch (parseError) {
        console.warn('⚠️ Failed to parse AI response as JSON, saving as plain text:', parseError);
        console.log('🔍 Cleaned response that failed to parse:', cleanedResponse);
        // If JSON parsing fails, save as a single message
        const response = await assistantApi.createAssistantMessage({
          assistantChatUserId,
          assistantMessageTypeId: 3, // Assistant type
          content: cleanedResponse,
        });

        if (!response || !response.assistantMessage) {
          throw new Error('Failed to save AI response - no response data');
        }

        console.log('✅ AI response saved as plain text with ID:', response.assistantMessage.id);
        // Update UI after successful save
        await fetchAssistantMessages(assistantChatUserId);
        return response.assistantMessage;
      }

      // Validate the parsed response structure
      console.log('🔍 Validating parsed response structure...');
      console.log('🔍 parsedResponse.content:', parsedResponse.content);
      console.log('🔍 parsedResponse.next_actions:', parsedResponse.next_actions);
      console.log('🔍 Is next_actions an array?', Array.isArray(parsedResponse.next_actions));

      if (!parsedResponse.content || !Array.isArray(parsedResponse.next_actions)) {
        console.warn('⚠️ Invalid JSON structure, missing content or next_actions array');
        console.log('🔍 parsedResponse.content exists:', !!parsedResponse.content);
        console.log(
          '🔍 parsedResponse.next_actions is array:',
          Array.isArray(parsedResponse.next_actions)
        );
        // Save as plain text if structure is invalid
        const response = await assistantApi.createAssistantMessage({
          assistantChatUserId,
          assistantMessageTypeId: 3, // Assistant type
          content: cleanedResponse,
        });

        if (!response || !response.assistantMessage) {
          throw new Error('Failed to save AI response - no response data');
        }

        console.log('✅ AI response saved as plain text with ID:', response.assistantMessage.id);
        // Update UI after successful save
        await fetchAssistantMessages(assistantChatUserId);
        return response.assistantMessage;
      }

      // Store the complete JSON response as a single assistant message
      console.log('📝 Creating JSON message with structured data...');
      console.log('🔍 Complete JSON to save:', JSON.stringify(parsedResponse, null, 2));

      const response = await assistantApi.createAssistantMessage({
        assistantChatUserId,
        assistantMessageTypeId: 3, // Assistant type
        content: JSON.stringify(parsedResponse), // Store as JSON string
      });

      if (!response || !response.assistantMessage) {
        throw new Error('Failed to save JSON response - no response data');
      }

      console.log('✅ JSON response saved with ID:', response.assistantMessage.id);
      console.log('🔍 JSON message details:', response.assistantMessage);

      // Update UI after successful save
      console.log('🔄 Updating UI with new messages...');
      await fetchAssistantMessages(assistantChatUserId);

      console.log('✅ AI response processing completed. Created 1 JSON message.');
      return response.assistantMessage;
    } catch (error) {
      console.error('❌ Error generating AI response:', error);
      const errorMsg =
        error instanceof Error ? error.message : 'Unknown error generating AI response';
      throw new Error(`Failed to generate AI response: ${errorMsg}`);
    }
  };

  const handleSendToInput = (content: string) => {
    if (!content || content.trim().length === 0) {
      toast.error('No content to send to input');
      return;
    }

    console.log('📝 Sending content to input:', content);
    setMessageText(content.trim());

    // Focus on the input field
    const inputElement = document.querySelector(
      'textarea[placeholder*="Type a message"]'
    ) as HTMLTextAreaElement;
    if (inputElement) {
      inputElement.focus();
      // Move cursor to end of text
      setTimeout(() => {
        inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length);
      }, 0);
    }

    toast.success('Content copied to input field');
  };

  const handleSendToChat = async (content: string) => {
    if (!content || content.trim().length === 0) {
      toast.error('No content to send');
      return;
    }

    if (!selectedRoom || !userData) {
      toast.error('No chat room selected');
      return;
    }

    try {
      console.log('📤 Sending content to chat via socket:', content);

      const trimmedContent = content.trim();

      // Use socket emission like the regular message sending
      if (onSendMessage) {
        onSendMessage(trimmedContent, 'text');
        console.log('✅ Message sent via socket successfully');
        toast.success('Message sent to chat');
      } else {
        throw new Error('Message sending function not available');
      }
    } catch (error) {
      console.error('❌ Error sending message to chat:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error sending message';
      toast.error(`Failed to send message: ${errorMsg}`);
    }
  };

  const generateResponseFromActions = async (selectedActions: string[]) => {
    if (!currentAssistantChatUser) {
      throw new Error('No active assistant chat user');
    }

    try {
      console.log('🚀 Generating AI response from selected actions...');
      console.log('🔍 Selected actions:', selectedActions);
      console.log('🔍 Current assistant content:', currentAssistantContent);

      // Create user message with concatenated selected actions
      const userMessageContent = selectedActions.join(' ');
      console.log('📝 Creating user message with content:', userMessageContent);

      const userMessage = await createUserMessage(currentAssistantChatUser.id, userMessageContent);
      console.log('✅ User message created with ID:', userMessage.id);

      // Generate AI response using the specified message structure
      const systemPrompt =
        'ให้ response ออกมาเป็น JSON มี Key "content" คือสรุป content ของ role assistent ที่ส่งไป + คำตอบที่ user ได้ถามทั้งหมดอย่างกระชับ และละเอียดจากข้อมูลที่มาจาก assistant และ Key "next_actions" คือ ให้สร้าง prompt สั้นๆ ที่บ่งบอกว่า user ควรทำอะไรต่อในขั้นตอนต่อไป เพื่อทำให้ความต้องการสมบูรณ์มากขึ้น มา 5 prompt เป็น list array';

      console.log('🔄 Calling AI service with conversation context...');
      const aiResponse = await generateChatText(
        [
          { role: 'system', content: systemPrompt },
          { role: 'assistant', content: currentAssistantContent },
          { role: 'user', content: userMessageContent },
        ],
        {
          model: 'openai/gpt-4o',
          max_tokens: 500,
          temperature: 0.7,
        }
      );

      if (!aiResponse || aiResponse.trim().length === 0) {
        throw new Error('No response from AI service or empty response');
      }

      console.log('✅ AI response generated, parsing JSON structure...');
      console.log('🔍 Raw AI response:', aiResponse);

      // Clean the AI response by removing ```json wrapper if present
      let cleanedResponse = aiResponse.trim();
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        console.log('🧹 Removed ```json wrapper from response');
      }
      console.log('🔍 Cleaned AI response:', cleanedResponse);

      // Parse and save the AI response using the same logic as generateAndSaveAIResponse
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(cleanedResponse);
        console.log('✅ Successfully parsed JSON:', parsedResponse);
      } catch (parseError) {
        console.warn('⚠️ Failed to parse AI response as JSON, saving as plain text:', parseError);
        const response = await assistantApi.createAssistantMessage({
          assistantChatUserId: currentAssistantChatUser.id,
          assistantMessageTypeId: 3, // Assistant type
          content: cleanedResponse,
        });

        if (!response || !response.assistantMessage) {
          throw new Error('Failed to save AI response - no response data');
        }

        console.log('✅ AI response saved as plain text with ID:', response.assistantMessage.id);
        // Update UI after successful save
        await fetchAssistantMessages(currentAssistantChatUser.id);
        return response.assistantMessage;
      }

      // Validate the parsed response structure
      if (!parsedResponse.content || !Array.isArray(parsedResponse.next_actions)) {
        console.warn('⚠️ Invalid JSON structure, saving as plain text');
        const response = await assistantApi.createAssistantMessage({
          assistantChatUserId: currentAssistantChatUser.id,
          assistantMessageTypeId: 3, // Assistant type
          content: cleanedResponse,
        });

        if (!response || !response.assistantMessage) {
          throw new Error('Failed to save AI response - no response data');
        }

        console.log('✅ AI response saved as plain text with ID:', response.assistantMessage.id);
        // Update UI after successful save
        await fetchAssistantMessages(currentAssistantChatUser.id);
        return response.assistantMessage;
      }

      // Store the complete JSON response as a single assistant message
      console.log('📝 Creating JSON message with structured data...');
      const response = await assistantApi.createAssistantMessage({
        assistantChatUserId: currentAssistantChatUser.id,
        assistantMessageTypeId: 3, // Assistant type
        content: JSON.stringify(parsedResponse), // Store as JSON string
      });

      if (!response || !response.assistantMessage) {
        throw new Error('Failed to save JSON response - no response data');
      }

      console.log('✅ JSON response saved with ID:', response.assistantMessage.id);
      console.log('✅ Response generation from actions completed.');

      // Update UI after successful save
      console.log('🔄 Updating UI with new messages...');
      await fetchAssistantMessages(currentAssistantChatUser.id);

      return response.assistantMessage;
    } catch (error) {
      console.error('❌ Error generating response from actions:', error);
      const errorMsg =
        error instanceof Error ? error.message : 'Unknown error generating response from actions';
      throw new Error(`Failed to generate response from actions: ${errorMsg}`);
    }
  };

  const formatAssistantMessageTime = (timestamp: string) => {
    // Use the same smart formatting logic as regular messages
    return formatTime(timestamp);
  };

  const renderAssistantMessages = () => {
    if (assistantLoading || aiLoading) {
      return (
        <>
          <AssistantChatUserSelector>
            <AssistantChatUserHeader>
              <AssistantChatUserLabel>Assistant Chat Sessions</AssistantChatUserLabel>
              <NewSessionButton
                onClick={() => setShowNewSessionModal(true)}
                disabled={assistantLoading || aiLoading}
              >
                <Plus size={12} />
                New Session
              </NewSessionButton>
            </AssistantChatUserHeader>
            {assistantChatUsers.length > 0 && (
              <AssistantChatUserSelect
                value={currentAssistantChatUser?.id || ''}
                onChange={e => handleAssistantChatUserChange(e.target.value)}
                disabled={assistantLoading || aiLoading || chatUsersLoading}
              >
                <option value="">เลือกเซสชั่น Chat...</option>
                {assistantChatUsers.map((chatUser: any) => (
                  <option key={chatUser.id} value={chatUser.id}>
                    {chatUser.name} (
                    {new Date(chatUser.updatedAt || chatUser.createdAt).toLocaleDateString()})
                  </option>
                ))}
              </AssistantChatUserSelect>
            )}
          </AssistantChatUserSelector>
          <AssistantLoadingContainer>
            <Loader2 size={20} className="animate-spin" />
            <span style={{ marginLeft: '8px' }}>
              {aiLoading ? 'AI is thinking...' : 'Loading assistant...'}
            </span>
          </AssistantLoadingContainer>
        </>
      );
    }

    if (aiError) {
      return (
        <>
          <AssistantChatUserSelector>
            <AssistantChatUserHeader>
              <AssistantChatUserLabel>Assistant Chat Sessions</AssistantChatUserLabel>
              <NewSessionButton
                onClick={() => setShowNewSessionModal(true)}
                disabled={assistantLoading || aiLoading}
              >
                <Plus size={12} />
                New Session
              </NewSessionButton>
            </AssistantChatUserHeader>
            {assistantChatUsers.length > 0 && (
              <AssistantChatUserSelect
                value={currentAssistantChatUser?.id || ''}
                onChange={e => handleAssistantChatUserChange(e.target.value)}
                disabled={assistantLoading || aiLoading || chatUsersLoading}
              >
                <option value="">เลือกเซสชั่น Chat...</option>
                {assistantChatUsers.map((chatUser: any) => (
                  <option key={chatUser.id} value={chatUser.id}>
                    {chatUser.name} (
                    {new Date(chatUser.updatedAt || chatUser.createdAt).toLocaleDateString()})
                  </option>
                ))}
              </AssistantChatUserSelect>
            )}
          </AssistantChatUserSelector>
          <AssistantEmptyState>
            <Bot size={48} />
            <h4>AI Error</h4>
            <p>There was an error with the AI service. Please try again.</p>
          </AssistantEmptyState>
        </>
      );
    }

    if (!currentAssistantChatUser) {
      return (
        <>
          <AssistantChatUserSelector>
            <AssistantChatUserHeader>
              <AssistantChatUserLabel>Assistant Chat Sessions</AssistantChatUserLabel>
              <NewSessionButton
                onClick={() => setShowNewSessionModal(true)}
                disabled={assistantLoading || aiLoading}
              >
                <Plus size={12} />
                New Session
              </NewSessionButton>
            </AssistantChatUserHeader>
            {assistantChatUsers.length > 0 && (
              <AssistantChatUserSelect
                value=""
                onChange={e => handleAssistantChatUserChange(e.target.value)}
                disabled={assistantLoading || aiLoading || chatUsersLoading}
              >
                <option value="">เลือกเซสชั่น Chat...</option>
                {assistantChatUsers.map((chatUser: any) => (
                  <option key={chatUser.id} value={chatUser.id}>
                    {chatUser.name} (
                    {new Date(chatUser.updatedAt || chatUser.createdAt).toLocaleDateString()})
                  </option>
                ))}
              </AssistantChatUserSelect>
            )}
          </AssistantChatUserSelector>
          <AssistantEmptyState>
            <Bot size={48} />
            <h4>AI Assistant</h4>
            <p>คลิกปุ่ม AI พร้อมข้อความเพื่อเริ่มการสนทนากับ Assistant</p>
          </AssistantEmptyState>
        </>
      );
    }

    if (assistantMessages.length === 0) {
      return (
        <>
          <AssistantChatUserSelector>
            <AssistantChatUserHeader>
              <AssistantChatUserLabel>Assistant Chat Sessions</AssistantChatUserLabel>
              <NewSessionButton
                onClick={() => setShowNewSessionModal(true)}
                disabled={assistantLoading || aiLoading}
              >
                <Plus size={12} />
                New Session
              </NewSessionButton>
            </AssistantChatUserHeader>
            {assistantChatUsers.length > 0 && (
              <AssistantChatUserSelect
                value={currentAssistantChatUser?.id || ''}
                onChange={e => handleAssistantChatUserChange(e.target.value)}
                disabled={assistantLoading || aiLoading || chatUsersLoading}
              >
                <option value="">เลือกเซสชั่น Chat...</option>
                {assistantChatUsers.map((chatUser: any) => (
                  <option key={chatUser.id} value={chatUser.id}>
                    {chatUser.name} (
                    {new Date(chatUser.updatedAt || chatUser.createdAt).toLocaleDateString()})
                  </option>
                ))}
              </AssistantChatUserSelect>
            )}
          </AssistantChatUserSelector>
          <AssistantEmptyState>
            <Bot size={48} />
            <h4>Ready to Help</h4>
            <p>
              Your AI assistant is ready. Messages will appear here as the conversation develops.
            </p>
          </AssistantEmptyState>
        </>
      );
    }

    // Filter messages: Type 1 (System) = hidden, Type 2 (User) = right, Type 3 (Assistant) = left
    // Also remove any Action messages (Type 4) as they are no longer used
    const visibleMessages = assistantMessages.filter(
      msg =>
        msg.assistantMessageType?.id !== 1 && // Hide system messages
        msg.assistantMessageType?.id !== 4 // Hide action messages (deprecated)
    );

    return (
      <>
        <AssistantChatUserSelector>
          <AssistantChatUserHeader>
            <AssistantChatUserLabel>Assistant Chat Sessions</AssistantChatUserLabel>
            <NewSessionButton
              onClick={() => setShowNewSessionModal(true)}
              disabled={assistantLoading || aiLoading}
            >
              <Plus size={12} />
              New Session
            </NewSessionButton>
          </AssistantChatUserHeader>
          {assistantChatUsers.length > 0 && (
            <AssistantChatUserSelect
              value={currentAssistantChatUser?.id || ''}
              onChange={e => handleAssistantChatUserChange(e.target.value)}
              disabled={assistantLoading || aiLoading || chatUsersLoading}
            >
              <option value="">Select a chat session...</option>
              {assistantChatUsers.map((chatUser: any) => (
                <option key={chatUser.id} value={chatUser.id}>
                  {chatUser.name} (
                  {new Date(chatUser.updatedAt || chatUser.createdAt).toLocaleDateString()})
                </option>
              ))}
            </AssistantChatUserSelect>
          )}
        </AssistantChatUserSelector>
        <AssistantMessagesContainer ref={assistantMessagesRef}>
          {visibleMessages.map((message: any) => {
            const isUser = message.assistantMessageType?.id === 2; // Type 2 = User

            // Try to parse JSON content for assistant messages
            let parsedContent = null;
            let displayContent = message.content;

            if (!isUser && message.assistantMessageType?.id === 3) {
              // Assistant message
              try {
                parsedContent = JSON.parse(message.content);
                // If successfully parsed, use the content field for display
                if (parsedContent && parsedContent.content) {
                  displayContent = parsedContent.content;
                }
              } catch (e) {
                // If parsing fails, use original content
                displayContent = message.content;
              }
            }

            const handleNextActionsClick = () => {
              if (
                parsedContent &&
                parsedContent.next_actions &&
                Array.isArray(parsedContent.next_actions)
              ) {
                setCurrentNextActions(parsedContent.next_actions);
                setCurrentAssistantContent(parsedContent.content || displayContent);
                setShowNextActionsModal(true);
              }
            };

            return (
              <AssistantMessage key={message.id} $isUser={isUser}>
                <AssistantMessageBubble $isUser={isUser} $isAction={false}>
                  {displayContent}
                </AssistantMessageBubble>

                {/* Render action buttons for assistant messages */}
                {!isUser &&
                  message.assistantMessageType?.id === 3 &&
                  displayContent &&
                  displayContent.trim().length > 0 && (
                    <AssistantMessageActions>
                      <MessageActionButton
                        onClick={() => handleSendToInput(displayContent)}
                        title="Send to Input"
                      >
                        <Edit3 size={12} />
                      </MessageActionButton>
                      <MessageActionButton
                        onClick={() => handleSendToChat(displayContent)}
                        title="Send to Chat"
                      >
                        <Send size={12} />
                      </MessageActionButton>
                    </AssistantMessageActions>
                  )}

                <AssistantMessageTime>
                  {formatAssistantMessageTime(message.updatedAt)}
                </AssistantMessageTime>

                {/* Render Next Actions button if JSON was parsed successfully */}
                {parsedContent &&
                  parsedContent.next_actions &&
                  Array.isArray(parsedContent.next_actions) &&
                  parsedContent.next_actions.length > 0 && (
                    <NextActionsButton onClick={handleNextActionsClick}>
                      Next Actions ({parsedContent.next_actions.length})
                    </NextActionsButton>
                  )}
              </AssistantMessage>
            );
          })}
        </AssistantMessagesContainer>
      </>
    );
  };

  // Listen for assistant message updates via socket
  useSocketEvent('send_notification', (data: any) => {
    try {
      if (data.service === 'assistant' && currentAssistantChatUser) {
        // Refresh assistant messages when there's an update
        fetchAssistantMessages(currentAssistantChatUser.id);
      }
    } catch (error) {
      console.error('Error handling assistant socket notification:', error);
    }
  });

  // Reset assistant state when room changes and fetch chat users
  useEffect(() => {
    setCurrentAssistantChatUser(null);
    setAssistantMessages([]);
    setAssistantChatUsers([]);
    setAssistantLoading(false);

    // Fetch existing assistant chat users for the new room
    if (selectedRoom && userData) {
      fetchAssistantChatUsers();
    }
  }, [selectedRoom?.id, userData?.id]);

  const closeSidebar = () => {
    setRightSidebarOpen(false);
  };

  // Action menu handlers
  const handleCreateTaskFromMessage = (message: Message) => {
    setSelectedMessage(message);
    setShowCreateTaskModal(true);
  };

  const handleDeleteMessage = (message: Message) => {
    setSelectedMessage(message);
    setShowDeleteModal(true);
  };

  const handleConfirmDeleteMessage = async (messageId: string) => {
    try {
      await chatMessageApi.deleteMessage(Number(messageId));
      toast.success('Message deleted successfully');

      // Update messages list in real-time if callback is provided
      if (onMessageDeleted) {
        onMessageDeleted(messageId);
      }
    } catch (error) {
      console.error('Error deleting message:', error);
      toast.error('Failed to delete message');
      throw error; // Re-throw to let modal handle loading state
    }
  };

  const handleTaskCreated = () => {
    toast.success('Task created successfully!');
    // TODO: Optionally navigate to the task or update UI
  };

  const handleSendLink = async (url: string) => {
    try {
      onSendMessage(url, 'link');
      toast.success('Link sent successfully!');
    } catch (error) {
      console.error('Error sending link:', error);
      toast.error('Failed to send link');
      throw error; // Re-throw to let modal handle loading state
    }
  };

  const toggleMessageActions = (messageId: string) => {
    setActiveMessageId(activeMessageId === messageId ? null : messageId);
  };

  const closeMessageActions = () => {
    setActiveMessageId(null);
  };

  // Close popover when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeMessageId) {
        // Check if the click is outside the message bubble and popover
        const target = event.target as Element;
        const messageElement = target.closest('.message-group');
        const popoverElement = target.closest('[role="menu"]');

        if (!messageElement && !popoverElement) {
          closeMessageActions();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeMessageId]);

  if (!selectedRoom) {
    return (
      <ChatContainer $sidebarOpen={rightSidebarOpen}>
        <ChatMainContent $sidebarOpen={rightSidebarOpen}>
          <EmptyState>
            <EmptyStateIcon>
              <MessageCircle size={64} />
            </EmptyStateIcon>
            <h3>เลือกการสนทนา</h3>
            <p>เลือกการสนทนาจากแถบด้านข้างเพื่อเริ่ม Chat</p>
          </EmptyState>
        </ChatMainContent>
      </ChatContainer>
    );
  }

  return (
    <ChatContainer $sidebarOpen={rightSidebarOpen}>
      {/* Mobile overlay for sidebar */}
      <MobileSidebarOverlay $isVisible={rightSidebarOpen} onClick={closeSidebar} />

      <ChatMainContent $sidebarOpen={rightSidebarOpen}>
        <ChatHeader>
          <ChatHeaderLeft>
            <MobileMenuButton onClick={onMobileSidebarOpen}>
              <Menu size={20} />
            </MobileMenuButton>
            <RoomAvatar $type={selectedRoom.type}>{getRoomIcon(selectedRoom.type)}</RoomAvatar>
            <div>
              <ChatTitle>
                {getRoomPrefix(selectedRoom.type)}
                {getRoomDisplayName(selectedRoom)}
              </ChatTitle>
            </div>
          </ChatHeaderLeft>

          <ChatHeaderActions>
            <SidebarToggleButton
              onClick={toggleUsersSidebar}
              title="Show users"
              $isActive={rightSidebarOpen && sidebarContent === 'users'}
            >
              <Users size={18} />
            </SidebarToggleButton>
            <SidebarToggleButton
              onClick={toggleSettingsSidebar}
              title="Chat settings"
              $isActive={rightSidebarOpen && sidebarContent === 'settings'}
            >
              <Settings size={18} />
            </SidebarToggleButton>
            {/* {selectedRoom.type === 'private' && (
              <>
                <ActionButton>
                  <Phone size={18} />
                </ActionButton>
                <ActionButton>
                  <Video size={18} />
                </ActionButton>
              </>
            )} */}
            {/* <ActionButton>
              <Info size={18} />
            </ActionButton> */}
            {/* <ActionButton>
              <MoreVertical size={18} />
            </ActionButton> */}
          </ChatHeaderActions>
        </ChatHeader>

        <MessagesContainer ref={messagesContainerRef}>
          {loading ? (
            <LoadingSkeleton />
          ) : (
            <>
              {loadingMore && hasMoreMessages && <LoadMoreSkeleton />}
              {messages.map(message => {
                // Compare senderId with current user's ID (handle string/number conversion)
                const isOwnMessage = userData
                  ? String(message.senderId) === String(userData.id)
                  : false;

                return (
                  <MessageGroup
                    key={message.id}
                    $isOwn={isOwnMessage}
                    className="message-group"
                    data-message-id={message.id}
                    data-sender-id={message.senderId}
                  >
                    {!isOwnMessage && (
                      <UserAvatar
                        $imageUrl={
                          shouldShowUserImage(message.user?.imageUrl)
                            ? message.user?.imageUrl || undefined
                            : undefined
                        }
                        title={getDisplayName(message)}
                      >
                        {!shouldShowUserImage(message.user?.imageUrl) &&
                          getUserInitials(message.user?.firstName, message.user?.lastName)}
                      </UserAvatar>
                    )}

                    <MessageContent $isOwn={isOwnMessage}>
                      {!isOwnMessage && (
                        <MessageHeader $isOwn={isOwnMessage}>
                          <SenderName>{getDisplayName(message)}</SenderName>
                        </MessageHeader>
                      )}

                      {/* Render image messages without balloon styling */}
                      {message.type === 'image' ? (
                        <div style={{ position: 'relative' }}>
                          <MessageImage
                            src={message.imageUrl || message.content}
                            alt="Shared image"
                            onClick={e => {
                              e.stopPropagation(); // Prevent bubble click when clicking image
                              handleImagePreview(message.imageUrl || message.content);
                            }}
                          />
                          <MessageActions $isOwn={isOwnMessage}>
                            <MessageActionPopover
                              message={message}
                              onCreateTask={handleCreateTaskFromMessage}
                              onDeleteMessage={handleDeleteMessage}
                              isOpen={activeMessageId === message.id}
                              onClose={closeMessageActions}
                              isOwnMessage={isOwnMessage}
                            />
                          </MessageActions>
                        </div>
                      ) : (
                        /* Render text and link messages with balloon styling */
                        <MessageBubble
                          $isOwn={isOwnMessage}
                          onClick={e => {
                            // Don't trigger if clicking on a link (which has its own click handler)
                            if (message.type !== 'link') {
                              e.stopPropagation();
                              toggleMessageActions(message.id);
                            }
                          }}
                        >
                          <MessageBubbleContent>
                            {message.type === 'link' ? (
                              <MessageLink
                                href={message.content}
                                target="_blank"
                                rel="noopener noreferrer"
                                color={isOwnMessage ? 'white' : 'primary'}
                                onClick={e => e.stopPropagation()}
                              >
                                <Link size={14} />
                                {message.content}
                              </MessageLink>
                            ) : (
                              // Use different text components for own vs received messages
                              isOwnMessage ? (
                                <MessageText
                                  dangerouslySetInnerHTML={{
                                    __html: processMessageContent(message.content)
                                  }}
                                />
                              ) : (
                                <ReceivedMessageText
                                  dangerouslySetInnerHTML={{
                                    __html: processMessageContent(message.content)
                                  }}
                                />
                              )
                            )}
                            <MessageActions $isOwn={isOwnMessage}>
                              <MessageActionPopover
                                message={message}
                                onCreateTask={handleCreateTaskFromMessage}
                                onDeleteMessage={handleDeleteMessage}
                                isOpen={activeMessageId === message.id}
                                onClose={closeMessageActions}
                                isOwnMessage={isOwnMessage}
                              />
                            </MessageActions>
                          </MessageBubbleContent>
                        </MessageBubble>
                      )}

                      <MessageFooter $isOwn={isOwnMessage}>
                        <ReadStatus
                          readStatus={message.readStatus}
                          isOwn={isOwnMessage}
                          isGroupChat={selectedRoom?.type !== 'private' || (selectedRoom?.chatUsers?.length || 0) > 2}
                          messageId={message.id}
                        />
                        <MessageTime $isOwn={isOwnMessage}>
                          {formatTime(message.timestamp)}
                        </MessageTime>
                        <MessageStatus
                          $status={message.status || 'delivered'}
                          $isOwn={isOwnMessage}
                        >
                          {getMessageStatusIcon(message.status || 'delivered')}
                        </MessageStatus>

                      </MessageFooter>
                    </MessageContent>
                  </MessageGroup>
                );
              })}
              <div ref={messagesEndRef} />
            </>
          )}
        </MessagesContainer>

        <MessageInput>
          {/* Image previews */}
          {imagePreviewUrls.length > 0 && (
            <ImagePreviewContainer>
              {imagePreviewUrls.map((url, index) => (
                <ImagePreview key={index}>
                  <PreviewImage src={url} alt={`Preview ${index + 1}`} />
                  <RemoveImageButton onClick={() => removeImage(index)}>
                    <X size={12} />
                  </RemoveImageButton>
                </ImagePreview>
              ))}
            </ImagePreviewContainer>
          )}

          {/* Upload progress indicator */}
          {isUploading && (
            <UploadingIndicator>
              <Loader2 size={14} />
              Uploading images...
            </UploadingIndicator>
          )}

          <InputContainer>
            <ImageUploadButton onClick={() => setShowAttachLinkModal(true)} title="Attach link">
              <Paperclip size={18} />
            </ImageUploadButton>
            <ImageUploadButton>
              <ImageIcon size={18} />
              <input type="file" accept="image/*" multiple onChange={handleImageSelect} />
            </ImageUploadButton>
            <RichTextEditor
              placeholder="พิมพ์ข้อความ..."
              value={messageHtml}
              onChange={(htmlContent, textContent) => {
                setMessageHtml(htmlContent);
                setMessageText(textContent);
              }}
              onKeyDown={e => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              maxHeight="120px"
              minHeight="40px"
              mentionUsers={selectedRoom?.chatUsers?.map(chatUser => ({
                id: chatUser.user.id,
                firstName: chatUser.user.firstName,
                lastName: chatUser.user.lastName,
                imageUrl: chatUser.user.imageUrl
              })) || []}
              enableMentions={true}
            />
            <SidebarToggleButton
              onClick={!assistantLoading && !aiLoading ? toggleAISidebar : undefined}
              title={assistantLoading || aiLoading ? 'AI กำลังดำเนินการ...' : 'AI Assistant'}
              $isActive={rightSidebarOpen && sidebarContent === 'ai'}
              $disabled={assistantLoading || aiLoading}
            >
              {assistantLoading || aiLoading ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <Bot size={18} />
              )}
            </SidebarToggleButton>
            <SendButton
              onClick={handleSendMessage}
              disabled={!messageText.trim() && selectedImages.length === 0}
            >
              <Send size={18} />
            </SendButton>
          </InputContainer>
        </MessageInput>
      </ChatMainContent>

      {/* Image Preview Modal */}
      {previewImage && (
        <ImagePreviewModal onClick={closeImagePreview}>
          <PreviewImageContainer onClick={e => e.stopPropagation()}>
            <FullScreenImage
              src={previewImage}
              alt="Full screen preview"
              $zoom={imageZoom}
              onClick={handleImageClick}
              draggable={false}
            />
            <CloseButton onClick={closeImagePreview}>
              <X size={20} />
            </CloseButton>

            <ZoomControls>
              <ZoomButton onClick={zoomIn} disabled={imageZoom >= 3}>
                +
              </ZoomButton>
              <ZoomButton onClick={resetZoom} disabled={imageZoom === 1}>
                ⌂
              </ZoomButton>
              <ZoomButton onClick={zoomOut} disabled={imageZoom <= 0.5}>
                −
              </ZoomButton>
            </ZoomControls>

            <ZoomIndicator>{Math.round(imageZoom * 100)}%</ZoomIndicator>
          </PreviewImageContainer>
        </ImagePreviewModal>
      )}

      {/* Right Sidebar */}
      <RightSidebar $isOpen={rightSidebarOpen}>
        <SidebarHeader>
          <SidebarTitle>
            {sidebarContent === 'users' ? 'ผู้ใช้' :
             sidebarContent === 'settings' ? 'การตั้งค่า Chat' : 'AI Assistant'}
          </SidebarTitle>
          <SidebarCloseButton onClick={closeSidebar}>
            <X size={18} />
          </SidebarCloseButton>
        </SidebarHeader>

        <SidebarContent>
          {sidebarContent === 'users' ? (
            <div>
              {selectedRoom?.chatUsers?.map(chatUser => (
                <UserListItem key={chatUser.user.id}>
                  <SidebarUserAvatar
                    $imageUrl={
                      shouldShowUserImage(chatUser.user.imageUrl)
                        ? chatUser.user.imageUrl || undefined
                        : undefined
                    }
                    title={`${chatUser.user.firstName} ${chatUser.user.lastName}`}
                  >
                    {!shouldShowUserImage(chatUser.user.imageUrl) &&
                      getUserInitials(chatUser.user.firstName, chatUser.user.lastName)}
                  </SidebarUserAvatar>
                  <UserInfo>
                    <UserName>
                      {chatUser.user.firstName} {chatUser.user.lastName}
                    </UserName>
                  </UserInfo>
                </UserListItem>
              ))}
              {(!selectedRoom?.chatUsers || selectedRoom.chatUsers.length === 0) && (
                <div
                  style={{
                    textAlign: 'center',
                    color: appTheme.colors.text.secondary,
                    padding: '2rem',
                  }}
                >
                  ไม่มีผู้ใช้ใน Chat นี้
                </div>
              )}
            </div>
          ) : sidebarContent === 'settings' ? (
            <div style={{ padding: appTheme.spacing.md }}>
              <ChatBotSettings
                isBot={selectedRoom?.isBot || false}
                botDuration={selectedRoom?.botDuration || 30}
                onSettingsChange={handleBotSettingsUpdate}
                loading={botSettingsLoading}
                compact={true}
              />
            </div>
          ) : (
            renderAssistantMessages()
          )}
        </SidebarContent>
      </RightSidebar>

      {/* Create Task from Message Modal */}
      <CreateTaskFromMessageModal
        isOpen={showCreateTaskModal}
        onClose={() => setShowCreateTaskModal(false)}
        messageContent={selectedMessage?.content || ''}
        onTaskCreated={handleTaskCreated}
      />

      {/* Delete Message Modal */}
      <DeleteMessageModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        message={selectedMessage}
        onConfirmDelete={handleConfirmDeleteMessage}
      />

      {/* Attach Link Modal */}
      <AttachLinkModal
        isOpen={showAttachLinkModal}
        onClose={() => setShowAttachLinkModal(false)}
        onSendLink={handleSendLink}
      />

      {/* New Session Modal */}
      <NewSessionModal
        isOpen={showNewSessionModal}
        onClose={() => setShowNewSessionModal(false)}
        onGenerate={handleNewSessionGenerate}
        initialText={messageText}
        loading={assistantLoading}
      />

      {/* Next Actions Modal */}
      <NextActionsModal
        isOpen={showNextActionsModal}
        onClose={() => setShowNextActionsModal(false)}
        actions={currentNextActions}
        onGenerate={generateResponseFromActions}
        isGenerating={aiLoading}
      />
    </ChatContainer>
  );
}
